<script setup>

// ref vs reactive
// reactive函数只能接收对象（引用数据类型）
// ref函数可以接收任何数据类型（简单数据类型和引用数据类型）

/*import {reactive, ref} from "vue";
const message = reactive({
  message: 'hello vue3'
})
console.log(message)
console.log(message.message)

const msg = ref('helloworld vue3')
console.log(msg)
console.log(msg.value)*/


// computed 计算属性 某一个值的结果是通过响应式的变量计算而来。computed是一个函数。有返回值

import {computed, ref, watch} from "vue";
/*
const count = ref(10)
const result = computed(() => {
  return count.value * 2
})
console.log(result)*/

/*const list = ref([])
list.value.push('abc')
list.value.push('aaa')
list.value.push('bbb')
list.value.push('java')
list.value.push('vue')

const resultList = computed(() => {
  return list.value.filter((item) => {
    return item.length <= 3
  })
})

resultList.value.forEach(item => console.log(item))*/

// watch
/*const count = ref(10)

watch(count, (newVal, oldVal) => {
  console.log(newVal, oldVal)
})

setInterval(() => {
  count.value = count.value * 2
}, 3000)*/


/*const count = ref(10)

watch(count, (newVal, oldVal) => {
  console.log(newVal, oldVal)
}, {
  immediate: true
})

setTimeout(() => {
  count.value = count.value * 2
}, 3000)*/

/*const user = ref({
  name: 'zhangsan',
  age: 23
})

watch(user, (newVal, oldVal) => {
  console.log(newVal, oldVal)
})

setTimeout(() => {
  user.value = {
    name: 'lisi',
    age: 24
  }
}, 3000)*/

const user = ref({
  name: 'zhangsan',
  age: 23
})

watch(user, (newVal, oldVal) => {
  // 24 24 ????
  console.log(newVal.age, oldVal.age)
}, {
  deep: true
})

setTimeout(() => {
  user.value.age = 24
}, 3000)


</script>

<template>

  <!--  在 Vue3中可以有多个根元素-->
  <div>
    <h1></h1>
  </div>

  <div>
    <h2></h2>
  </div>
</template>

<style scoped></style>
